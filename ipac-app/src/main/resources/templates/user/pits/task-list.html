<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout" layout:decorate="~{base/layout}">
<div class="wrapper py-0" layout:fragment="content">

    <div class="container-fluid">
        <div class="page-title-box py-3">
            <div class="row align-items-center">
                <div class="col-sm-12">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a th:href="@{/home}" href="index.html"><i class="mdi mdi-home-outline"></i></a>
                        </li>
                        <li class="breadcrumb-item">Personal Center</li>
                        <li class="breadcrumb-item active">My Files</li>
                    </ol>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-2">
                <div class="card card-box">
                    <div class="card-body">
                        <div th:replace="~{user/left-common::div}"></div>
                    </div>
                </div>
            </div>
            <div class="col-lg-10">
                <div class="card card-box">
                    <div class="card-body">
                        <div class="d-flex align-items-center justify-content-between mb-3">
                            <h4 class="m-0 header-title"><i></i> PITS task list</h4>
                            <span><a th:href="@{/pits/main}" href="index-ipp.html" class="btn btn-info btn-sm">New task</a></span>
                        </div>

                        <form class="form-inline form-task" th:action="@{/pits/tasks}" method="get" id="search-form">
                            <label class="mx-2 font-12">Status</label>
                            <select class="custom-select custom-select-sm" name="statusDesc">
                                <option value=""></option>
                                <th:block th:each="item : ${codeDescMap}">
                                    <option th:text="${item.value}" th:if="${item.key != 0}"
                                            th:value="${item.key}"
                                            th:selected="${search.statusDesc eq item.key}">
                                    </option>
                                </th:block>
                            </select>
                            <label class="mx-2 font-12">Creation time</label>
                            <div class="input-daterange input-group" id="date-range">

                                <input type="text" autocomplete="off" class="form-control form-control-sm date_input" name="createTimeStart" th:value="${#dates.format(search.createTimeStart,'yyyy/MM/dd')}"/>
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                </div>
                                <input type="text" autocomplete="off" class="form-control form-control-sm date_input" name="createTimeEnd" th:value="${#dates.format(search.createTimeEnd,'yyyy/MM/dd')}"/>
                                <div class="input-group-append">
                                    <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">Search</button>
                        </form>
                        <div class="table-responsive">
                            <table class="table table-hover table-striped table-nowrap table-task font-12">
                                <thead>
                                <tr>
                                    <th scope="col">Task ID</th>
                                    <th scope="col" width="120">Remark</th>
                                    <th scope="col">Creation time</th>
                                    <th scope="col">Status</th>
                                    <th scope="col">Status time</th>
                                    <th scope="col">Consuming</th>
                                    <th scope="col">Files</th>
                                    <th scope="col">Action</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr th:if="${!#lists.isEmpty(page.content)}" th:each="vo,stat1 : ${page.content}">
                                    <td>[[${vo.userTask.taskId}]]</td>
                                    <td>
                                        <div>
                                            <span th:title="${vo.userTask.remark}" style="display: inline-block;overflow: hidden; width: 90px;white-space: nowrap; text-overflow: ellipsis;">[[${vo.userTask.remark}]]</span>
                                            <a href="javascript:void(0);" onclick="editRemarkShow(this);"><i class="mdi mdi-pencil m-0"></i></a>
                                        </div>
                                        <div class="input-group input-group-sm d-none">
                                            <input type="text" class="form-control" th:value="${vo.userTask.remark}">
                                            <div class="input-group-append">
                                                <span class="input-group-text bg-white" style="padding:.2rem .5rem;"><a href="javascript:void(0);" th:onclick="saveRemark([[${vo.userTask.id}]], this)"><i class="mdi mdi-check m-0"></i></a></span>
                                            </div>
                                        </div>
                                    </td>
                                    <td th:text="${#dates.format(vo.userTask.createTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td>
                                        <th:block th:switch="${vo.userTask.status}">
                                            <th:block th:case="1">
                                                <span class="text-warning"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="2">
                                                <span class="text-info"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="3">
                                                <span class="text-success"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="4">
                                                <span class="text-success"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="5">
                                                <span class="text-success"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="6">
                                                <span class="text-success"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="7">
                                                <span class="text-success"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="-1">
                                                <span class="text-danger" data-toggle="tooltip" data-container="body" th:title="${vo.userTask.errMsg}"><i class="mdi mdi-checkbox-blank-circle"></i> [[${codeDescMap.get(vo.userTask.status)}]]</span>
                                            </th:block>
                                            <th:block th:case="*">
                                            </th:block>
                                        </th:block>
                                    </td>
                                    <td th:text="${#dates.format(vo.userTask.modifyTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                                    <td th:text="${vo.useTime}"></td>
                                    <td>
                                        <div class="files-inline-wrapper">
                                            <!-- Single row layout for PITS -->
                                            <div class="files-inline-row">
                                                <!-- Input file for PITS -->
                                                <th:block th:if="${vo?.inputPath?.inputFile?.file?.name}">
                                                    <span class="file-item input-file" data-toggle="tooltip" data-container="body" th:title="${vo?.inputPath?.inputFile?.file?.name}">
                                                        <i class="mdi mdi-file text-info"></i>
                                                        <span class="file-name">[[${vo?.inputPath?.inputFile?.file?.name}]]</span>
                                                        <small class="file-size">([[${vo?.inputPath?.inputFile?.file?.size}]])</small>
                                                    </span>
                                                </th:block>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="action">
                                            <!--                                            <a href="javascript:void(0);" th:onclick="reanalysis([[${vo.userTask.id}]])" class="text-info" data-toggle="tooltip" title="Re-analysis"><i class="mdi mdi-cloud-sync h5 m-0"></i></a>-->
                                            <a href="javascript:void(0);" th:onclick="deleteTask([[${vo.userTask.id}]])" class="text-danger" data-toggle="tooltip" title="Delete"><i class="mdi mdi-close h5 m-0"></i></a>
                                            <th:block th:if="${5 eq vo.userTask.status}">
                                                <a href="javascript:void(0);" th:onclick="downLoadResult([[${vo.userTask.id}]])" data-toggle="tooltip" class="text-secondary" title="Results Download"><i class="mdi mdi-folder-download h5 m-0"></i></a>
                                            </th:block>
                                        </div>
                                    </td>
                                </tr>
                                <tr th:if="${#lists.isEmpty(page.content)}">
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <div th:insert="~{base/pageable}"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</html>
<th:block layout:fragment="custom-style">
    <style>
        .files-inline-wrapper {
            min-width: 200px;
        }

        .files-inline-row {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap;
        }

        .file-item {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            padding: 2px 6px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            max-width: 150px;
        }

        .file-item .file-name {
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100px;
            font-weight: 500;
        }

        .file-item .file-size {
            color: #6c757d;
            font-size: 0.65rem;
        }

        .file-item i {
            font-size: 12px;
            flex-shrink: 0;
        }

        .input-file {
            background: #e8f5e8;
            border-color: #4caf50;
        }
    </style>
</th:block>
<th:block layout:fragment="custom-script">
    <script th:inline="javascript">

        $(document).ready(function () {
            $('.date_input').datepicker({
                format: "yyyy/mm/dd",
                toggleActive: true,
                autoclose: true,
                todayHighlight: true
            });

            $('[data-toggle="tooltip"]').tooltip();

            // Handle file expansion (for future use if needed)
            $('.expand-files').click(function() {
                var $this = $(this);
                var $hiddenFiles = $this.siblings('.hidden-files');
                var isExpanded = $this.attr('aria-expanded') === 'true';

                if (isExpanded) {
                    $hiddenFiles.hide();
                    $this.attr('aria-expanded', 'false');
                    $this.find('span').text($this.find('span').text().replace('- less', '+ more'));
                } else {
                    $hiddenFiles.show();
                    $this.attr('aria-expanded', 'true');
                    $this.find('span').text($this.find('span').text().replace('+ more', '- less'));
                }
            });
        })


        function deleteTask(id) {
            layer.confirm('<p class="text-center">Are you sure to delete it？</p>', {btn: ['confirm', 'cancel']}, function () {
                var loadLayerIndex;
                $.ajax({
                    url: "/usercenter/deleteTask",
                    data: {"id": id},
                    dataType: 'json',
                    async: false,
                    method: 'post',
                    beforeSend: function () {
                        loadLayerIndex = layer.load(1, {
                            shade: [0.1, '#fff'] //0.1透明度的白色背景
                        });
                    },
                    success: function (result) {
                        if (result.code == 200) {
                            layer.msg("Delete successful", {time: 500}, function () {
                                location.reload();
                            });
                        } else {
                            layer.alert(result.message, {icon: 2});
                        }
                    },
                    complete: function () {
                        layer.close(loadLayerIndex);
                    }
                });
            });
        }

        function downLoadResult(id) {
            var _context_path = $("meta[name='_context_path']").attr("content");
            window.location.href = _context_path + "/pits/downLoadResult?id=" + id;
        }

        function editRemarkShow(_this) {
            $(_this).parents("td:first").children().eq(0).addClass("d-none");
            $(_this).parents("td:first").children().eq(1).removeClass("d-none");
        }

        function saveRemark(id, _this) {
            $(_this).parents("td:first").children().eq(0).removeClass("d-none");
            $(_this).parents("td:first").children().eq(1).addClass("d-none");
            var v = $(_this).parents("td:first").children().eq(1).find('input').val();
            $(_this).parents("td:first").children().eq(0).find('span').text(v);
            $(_this).parents("td:first").children().eq(0).find('span').attr('title', v);

            $.ajax({
                url: "/usercenter/venas/saveRemark",
                data: {"id": id, "remark": v},
                dataType: 'json',
                async: false,
                method: 'post',
                beforeSend: function () {
                    loadLayerIndex = layer.load(1, {
                        shade: [0.1, '#fff'] //0.1透明度的白色背景
                    });
                },
                success: function (result) {
                    if (result.code == 200) {
                        layer.msg("Save successful");
                    } else {
                        layer.alert(result.message, {icon: 2});
                    }
                },
                complete: function () {
                    layer.close(loadLayerIndex);
                }
            });
        }

    </script>
</th:block>
