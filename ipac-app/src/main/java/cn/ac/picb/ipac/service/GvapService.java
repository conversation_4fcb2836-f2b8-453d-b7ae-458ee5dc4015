package cn.ac.picb.ipac.service;

import cn.ac.picb.ipac.common.core.ServiceException;
import cn.ac.picb.ipac.common.enums.GvapTaskStatusEnum;
import cn.ac.picb.ipac.common.enums.TaskType;
import cn.ac.picb.ipac.common.util.DateUtils;
import cn.ac.picb.ipac.common.util.DownloadUtils;
import cn.ac.picb.ipac.common.util.IdUtil;
import cn.ac.picb.ipac.config.CustomReadListener;
import cn.ac.picb.ipac.config.FileProperties;
import cn.ac.picb.ipac.dto.FtpFileDTO;
import cn.ac.picb.ipac.dto.GvapFileExcelData;
import cn.ac.picb.ipac.dto.GvapTaskDTO;
import cn.ac.picb.ipac.dto.VenasTaskSearch;
import cn.ac.picb.ipac.model.User;
import cn.ac.picb.ipac.model.UserTask;
import cn.ac.picb.ipac.mq.gvap.GvapMessageSender;
import cn.ac.picb.ipac.mq.msg.*;
import cn.ac.picb.ipac.repository.UserTaskRepository;
import cn.ac.picb.ipac.vo.UserTaskVo;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelCommonException;
import com.alibaba.fastjson.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.criteria.Predicate;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributeView;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;

/**
 * <AUTHOR> Li
 * @date 2025/8/20
 */
@Service
@Slf4j
public class GvapService extends TaskService {

    @Autowired
    private FileProperties fileProperties;

    @Autowired
    private UserTaskRepository userTaskRepository;

    @Autowired
    private GvapMessageSender messageSender;

    public UserTask addTask(User user, GvapTaskDTO dto) {
        // 保存数据到数据库
        UserTask userTask = saveTaskToDb(user, dto);

        // 发送创建消息
        messageSender.sendTaskCreateMsg(new TaskCreateMsg(user.getAccountName(), userTask.getTaskId()));

        return userTask;
    }

    @Transactional(rollbackFor = Exception.class)
    public UserTask saveTaskToDb(User user, GvapTaskDTO dto) {
        UserTask userTask = new UserTask();
        userTask.setId(IdUtil.getShortUUID());
        String taskId = "GV" + getTaskId();
        userTask.setTaskId(taskId);
        userTask.setUser(user);
        Date date = new Date();
        userTask.setType(TaskType.gvap.name());
        userTask.setCreateTime(date);
        userTask.setStatus(GvapTaskStatusEnum.draft.getCode());
        userTask.setInputPath(JSONObject.toJSONString(dto));
        userTaskRepository.saveAndFlush(userTask);

        addTaskStatusFlow(GvapTaskStatusEnum.draft.getCode(), userTask);

        return userTask;
    }

    public void handlerStatusChange(StatusMsg msg) {
        String taskId = msg.getTaskId();
        Integer statusCode = msg.getStatusCode();

        if (statusCode == 20000) {
            log.error("分析服务分析出错：{}", msg.getMessage());
        } else if (statusCode == 30000) {
            log.error("task 移动文件出错：{}", msg.getMessage());
        }
        GvapTaskStatusEnum statusEnum = GvapTaskStatusEnum.getEnum(statusCode);
        if (statusEnum == null) {
            statusEnum = GvapTaskStatusEnum.error;
        }

        UserTask task = findByTaskId(taskId);

        // 已删除，不处理
        if (GvapTaskStatusEnum.delete.getCode().equals(task.getStatus())) {
            log.info("任务 {} 已被删除，不做处理", taskId);
            return;
        }
        // 防止重复消费消息
        if (!GvapTaskStatusEnum.error.equals(statusEnum) && task.getStatus() > statusEnum.getCode()) {
            log.info("任务 {} 出错或收到重复消息，status：{}", taskId, task.getStatus());
            return;
        }
        task.setStatus(statusEnum.getCode());
        task.setModifyTime(new Date());

        if (GvapTaskStatusEnum.ready.equals(statusEnum)) {
            // 发送分析任务开始消息
            // 发送开始的消息
            messageSender.sendAnalysisStartMsg(new AnalysisStartMsg(taskId));
            task.setStartTime(new Date());
        } else if (GvapTaskStatusEnum.complete.equals(statusEnum)) {
            task.setOutPath("/" + task.getTaskId());
            task.setEndTime(new Date());
            messageSender.sendTaskFinishedMsg(new TaskFinishedMsg(task.getId(), task.getTaskId()));
        } else if (GvapTaskStatusEnum.error.equals(statusEnum)) {
            task.setErrMsg(msg.getMessage());
        }
        userTaskRepository.save(task);

        addTaskStatusFlow(statusCode, task);
    }

    public void handlerTaskCreate(TaskCreateMsg msg) {
        String taskId = msg.getTaskId();
        UserTask task = findByTaskId(taskId);
        GvapTaskDTO dto = JSONUtil.toBean(task.getInputPath(), GvapTaskDTO.class);

        // 输入输出文件夹
        File inputDir = FileUtil.file(fileProperties.getInputHome(), fileProperties.getGvapDirName(), taskId);

        // 把输入的文件拷贝的文件复制进去
        File refFile = FileUtil.file(inputDir, dto.getRefFile().getSampleName() + ".fasta");
        String username = msg.getUsername();
        String ftpHome = fileProperties.getFtpHome() + File.separator + username;

        FileUtil.copy(
                FileUtil.file(ftpHome, dto.getRefFile().getFile().getPath()),
                refFile,
                true
        );
        List<String> inputPaths = new ArrayList<>();

        for (GvapTaskDTO.GvapRow inputFile : dto.getInputFiles()) {
            String targetName = inputFile.getSampleName() + ".fasta";
            FileUtil.copy(
                    FileUtil.file(ftpHome, inputFile.getFile().getPath()),
                    FileUtil.file(inputDir, targetName),
                    true
            );
            inputPaths.add(StrUtil.format("{}/{}/{}", fileProperties.getGvapServerInputDataHome(), taskId, targetName));
        }

        // 写入到params.json
        cn.hutool.json.JSONObject entries = new cn.hutool.json.JSONObject();
        entries.put("input", CollUtil.join(inputPaths, ";"));
        entries.put("ref", StrUtil.format("{}/{}/{}", fileProperties.getPitsServerInputDataHome(), taskId, refFile.getName()));
        entries.put("outdir", StrUtil.format("{}/{}", fileProperties.getGvapServerOutputDataHome(), taskId));
        FileUtil.writeUtf8String(entries.toStringPretty(), FileUtil.file(inputDir, "params.json"));
    }


    public void handlerAnalysisResult(AnalysisResultMsg msg) {
        String taskId = msg.getTaskId();
        // 压缩文件
        File outputDir = FileUtil.file(fileProperties.getOutputHome(), fileProperties.getGvapDirName(), taskId);
        List<File> resultFiles = FileUtil.loopFiles(outputDir, new FileFilter() {
            @Override
            public boolean accept(File pathname) {
                if (pathname.getAbsolutePath().contains("qsub")) {
                    return false;
                } else {
                    return true;
                }
            }
        });
        File zipFile = FileUtil.file(outputDir, "gvap_result.zip");
        ZipUtil.zip(zipFile, true, resultFiles.toArray(new File[0]));
    }

    public Page<UserTaskVo> findGvapTaskPage(User user, Pageable pageable, VenasTaskSearch search) {
        Page<UserTask> page = userTaskRepository.findAll((root, query, cb) -> {
            List<Predicate> predicateList = new ArrayList<>();
            if (StringUtils.isNoneBlank(search.getTaskId())) {
                predicateList.add(cb.equal(root.get("taskId"), search.getTaskId()));
            }
            predicateList.add(cb.equal(root.get("type"), TaskType.gvap.name()));
            if (StringUtils.isNoneBlank(search.getStatusDesc())) {
                predicateList.add(cb.equal(root.get("status"), search.getStatusDesc()));
            }
            if (search.getCreateTimeStart() != null) {
                predicateList.add(cb.greaterThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeStart(), "yyyy-MM-dd") + " 00:00:00")));
            }
            if (search.getCreateTimeEnd() != null) {
                predicateList.add(cb.lessThanOrEqualTo(root.get("createTime"), DateUtils.parseDate(DateUtils.formatDate(search.getCreateTimeEnd(), "yyyy-MM-dd") + " 23:59:59")));
            }
            predicateList.add(cb.equal(root.get("user").get("id"), user.getId()));
            predicateList.add(cb.notEqual(root.get("status"), GvapTaskStatusEnum.delete.getCode()));
            query.where(predicateList.toArray(new Predicate[]{}));
            query.orderBy(cb.desc(root.get("createTime")));
            return null;
        }, pageable);
        List<UserTask> content = page.getContent();
        List<UserTaskVo> voList = new ArrayList<>();
        for (UserTask userTask : content) {
            UserTaskVo vo = new UserTaskVo();
            voList.add(vo);
            vo.setUserTask(userTask);
            if (StrUtil.isNotBlank(userTask.getInputPath())) {
                vo.setInputPath(JSONObject.parseObject(userTask.getInputPath()));
            }
            vo.setUseTime(getUseTime(userTask.getCreateTime(), userTask.getModifyTime()));
        }
        return new PageImpl<>(voList, pageable, page.getTotalElements());
    }

    /**
     * 从GVAP任务的inputPath中提取文件列表
     *
     * @param userTask 用户任务
     * @return 文件列表，格式为List<List<String>>
     */
    private List<List<String>> getGvapFilesList(UserTask userTask) {
        List<List<String>> filesList = new ArrayList<>();
        try {
            if (StringUtils.isNotBlank(userTask.getInputPath())) {
                GvapTaskDTO dto = JSONUtil.toBean(userTask.getInputPath(), GvapTaskDTO.class);

                if (dto != null) {
                    // 添加参考文件
                    if (dto.getRefFile() != null && dto.getRefFile().getFile() != null
                            && StringUtils.isNotBlank(dto.getRefFile().getFile().getName())) {
                        List<String> refFileList = new ArrayList<>();
                        refFileList.add(dto.getRefFile().getFile().getName());
                        filesList.add(refFileList);
                    }

                    // 添加输入文件
                    if (dto.getInputFiles() != null && !dto.getInputFiles().isEmpty()) {
                        for (GvapTaskDTO.GvapRow inputFile : dto.getInputFiles()) {
                            if (inputFile != null && inputFile.getFile() != null
                                    && StringUtils.isNotBlank(inputFile.getFile().getName())) {
                                List<String> inputFileList = new ArrayList<>();
                                inputFileList.add(inputFile.getFile().getName());
                                filesList.add(inputFileList);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析GVAP任务文件列表失败: {}", e.getMessage(), e);
        }
        return filesList;
    }

    public void downloadResult(String id, String userId, HttpServletRequest request, HttpServletResponse response) throws IOException {
        UserTask task = findUserTaskById(id);
        if (!task.getUser().getId().equals(userId)) {
            throw new ServiceException(StrUtil.format("任务创建人ID和当前用户ID不一致：{} {}", task.getUser().getId(), userId));
        }
        String taskId = task.getTaskId();
        File file = FileUtil.file(fileProperties.getOutputHome(), fileProperties.getGvapDirName(), taskId, "gvap_result.zip");
        DownloadUtils.download(request, response, file);
    }

    public void downloadTemplateExcel(HttpServletRequest request, HttpServletResponse response) throws IOException {
        File file = FileUtil.file(fileProperties.getInputHome(), "gvap_template", "gvap_template.xlsx");
        if (FileUtil.exist(file)) {
            DownloadUtils.download(request, response, file);
            return;
        }
        FileUtil.touch(file);
        EasyExcel.write(file, GvapFileExcelData.class).sheet("data").doWrite(Collections.emptyList());
        DownloadUtils.download(request, response, file);
    }

    public GvapTaskDTO uploadDataExcel(MultipartFile multipartFile, String username) {
        String ftpHome = fileProperties.getFtpHome() + File.separator + username;
        List<GvapFileExcelData> rows;
        try {
            rows = EasyExcel.read(multipartFile.getInputStream(), new CustomReadListener()).head(GvapFileExcelData.class).sheet().doReadSync();
        } catch (IOException | ExcelCommonException e) {
            e.printStackTrace();
            throw new ServiceException("template error, please use the correct template file");
        } catch (ExcelAnalysisException e) {
            e.printStackTrace();
            throw new ServiceException("excel header error, please use the correct template file");
        }

        Set<String> names = new HashSet<>();
        int refCount = 0;
        GvapFileExcelData refRow = null;
        ArrayList<GvapFileExcelData> inputRows = new ArrayList<>();

        for (GvapFileExcelData row : rows) {
            String sampleName = row.getSampleName();
            String path = row.getFastaFile();
            String type = row.getType();
            if (!StrUtil.endWithAny(path, ".fa", ".fasta")) {
                throw new ServiceException("selected file type error: " + path);
            }
            if (!StrUtil.equalsAny(type, "ref", "input")) {
                throw new ServiceException(StrUtil.format("Type: {} error, only ref and input can be filled in", type));
            }
            if (StrUtil.equals(type, "ref")) {
                refRow = row;
                refCount++;
                if (refCount > 1) {
                    throw new ServiceException("There can only be one ref file, and there can be one if the type is a ref row in excel");
                }
            } else {
                inputRows.add(row);
            }

            if (!names.add(sampleName)) {
                throw new ServiceException(StrUtil.format("Sample Name {} repeat", sampleName));
            }
        }

        GvapTaskDTO result = new GvapTaskDTO();

        List<GvapTaskDTO.GvapRow> inputFiles = new ArrayList<>();
        for (GvapFileExcelData inputRow : inputRows) {
            GvapTaskDTO.GvapRow item = new GvapTaskDTO.GvapRow();
            item.setSampleName(inputRow.getSampleName());
            item.setFile(obtainFileVo(ftpHome, inputRow.getFastaFile()));
            inputFiles.add(item);
        }
        if (refRow != null) {
            GvapTaskDTO.GvapRow item = new GvapTaskDTO.GvapRow();
            item.setSampleName(refRow.getSampleName());
            item.setFile(obtainFileVo(ftpHome, refRow.getFastaFile()));
            result.setRefFile(item);
        }

        result.setInputFiles(inputFiles);

        return result;
    }

    @SneakyThrows
    private FtpFileDTO obtainFileVo(String rootPath, String path) {
        if (StrUtil.isBlank(path)) {
            return null;
        }
        final File file = new File(rootPath, path);

        if (!FileUtil.isSymlink(file) && !FileUtil.exist(file)) {
            throw new ServiceException(StrUtil.format("{}, path error", path));
        }

        path = StrUtil.startWith(path, "/") ? path : "/" + path;
        FtpFileDTO vo = new FtpFileDTO();
        vo.setName(file.getName());
        vo.setPath(path);

        BasicFileAttributeView view = Files.getFileAttributeView(Paths.get(file.toURI()), BasicFileAttributeView.class, LinkOption.NOFOLLOW_LINKS);
        BasicFileAttributes attributes = view.readAttributes();
        vo.setSize(FileUtil.readableFileSize(attributes.size()));
        return vo;
    }
}
