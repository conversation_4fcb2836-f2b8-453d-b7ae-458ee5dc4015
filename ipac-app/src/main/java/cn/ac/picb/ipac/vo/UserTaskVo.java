package cn.ac.picb.ipac.vo;

import cn.ac.picb.ipac.model.TaskStatusFlow;
import cn.ac.picb.ipac.model.UserTask;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.List;

@Data
public class UserTaskVo {
    private UserTask userTask;
    /**
     * 最新状态
     */
    private TaskStatusFlow taskStatusFlow;

    private List<List<String>> files;

    private JSONObject inputPath;

    /**
     * 耗时（最新状态时间-任务创建时间）
     *  耗时在计算的时候，如果不超过1h的话，就显示分钟；如果超过1h的话，就显示m.n小时
     *  小于1分钟的话，就显示多少秒
     *
     */
    private String useTime;

}
